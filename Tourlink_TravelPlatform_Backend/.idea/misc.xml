<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/attraction-service/pom.xml" />
        <option value="$PROJECT_DIR$/data-platform-service/pom.xml" />
        <option value="$PROJECT_DIR$/gateway-service/pom.xml" />
        <option value="$PROJECT_DIR$/routing-service/pom.xml" />
        <option value="$PROJECT_DIR$/social-service/pom.xml" />
        <option value="$PROJECT_DIR$/user-service/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>