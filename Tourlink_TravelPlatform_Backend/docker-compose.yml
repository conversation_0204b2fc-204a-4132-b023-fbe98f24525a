services:
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: nacos
    environment:
      - MODE=standalone
      - NACOS_APPLICATION_PORT=8848
      - JVM_XMS=256m
      - JVM_XMX=512m
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    volumes:
      - ./nacos:/home/<USER>/logs
    restart: always
    networks:
      - tourlinkwork
    healthcheck: # 🔍 健康检查，启动等待
      test: [ "CMD", "curl", "-f", "http://localhost:8848/nacos" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 40s  # 等待 nacos 完整启动

  attraction-service:
    build:
      context: ./attraction-service
      dockerfile: Dockerfile
    ports:
      - "9080:9080"
    container_name: attraction-service
    networks:
      - tourlinkwork
    depends_on:
      nacos:
        condition: service_healthy
    restart: always

  data-platform-service:
    build:
      context: ./data-platform-service
      dockerfile: Dockerfile
    ports:
      - "9081:9081"
    container_name: data-platform-service
    networks:
      - tourlinkwork
    depends_on:
      nacos:
        condition: service_healthy
    restart: always


  routing-service:
    build:
      context: ./routing-service
      dockerfile: Dockerfile
    ports:
      - "9083:9083"
    container_name: routing-service
    networks:
      - tourlinkwork
    depends_on:
      nacos:
        condition: service_healthy
    restart: always

  social-service:
    build:
      context: ./social-service
      dockerfile: Dockerfile
    ports:
      - "9084:9084"
    container_name: social-service
    networks:
      - tourlinkwork
    depends_on:
      nacos:
        condition: service_healthy
    restart: always


  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    ports:
      - "9085:9085"
    container_name: user-service
    networks:
      - tourlinkwork
    depends_on:
      nacos:
        condition: service_healthy
    restart: always

networks:
  tourlinkwork:

