server:
  port: 9083
spring:
  application:
    name: routing-service
  cloud:
    openfeign:
      client:
        config:
          default:
            connect-timeout: 5000
            read-timeout: 8000
          attraction-service:
            url: http://attraction-service:9080
          data-platform-service:
            url: http://data-platform-service:9081
          social-service:
            url: http://social-service:9084

  datasource:
    url: ***************************************************************************************************************
    username: root
    password: TourLink@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update